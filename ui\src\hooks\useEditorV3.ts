/**
 * Production-ready hook for EditorV3 core logic
 * Extracts and modularizes the main editor functionality
 */

import { useState, useRef, useCallback, useEffect } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export type ViewMode = 'preview' | 'code';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'plan' | 'code' | 'message';
}

export interface Page {
  id: string;
  name: string;
  content: string;
  isActive: boolean;
  lastUpdated?: Date;
}

export interface ElementInfo {
  selector: string;
  tagName: string;
  textContent: string;
  attributes: Record<string, string>;
  isNavigation: boolean;
  isInteractive: boolean;
  implementationType?: string;
  implementationReason?: string;
  intentData?: {
    userIntent: string;
    suggestion?: string;
  };
}

export interface EditorState {
  // Content state
  htmlContent: string;
  streamingContent: string;
  stableIframeContent: string;

  // UI state
  viewMode: ViewMode;
  isGenerating: boolean;
  isLinking: boolean;

  // Multi-page state
  pages: Page[];
  currentPageId: string;

  // Chat state
  messages: ChatMessage[];
  input: string;

  // Selection state
  selectedElement: ElementInfo | null;
  showImplementModal: boolean;
}

export interface EditorActions {
  // Content actions
  setHtmlContent: (content: string) => void;
  setStreamingContent: (content: string) => void;
  setStableIframeContent: (content: string) => void;

  // UI actions
  setViewMode: (mode: ViewMode) => void;
  setIsGenerating: (generating: boolean) => void;
  setIsLinking: (linking: boolean) => void;

  // Multi-page actions
  addPage: (page: Omit<Page, 'lastUpdated'>) => void;
  updatePage: (pageId: string, updates: Partial<Page>) => void;
  switchToPage: (pageId: string) => void;

  // Chat actions
  addMessage: (message: ChatMessage) => void;
  setInput: (input: string) => void;
  clearInput: () => void;

  // Selection actions
  setSelectedElement: (element: ElementInfo | null) => void;
  setShowImplementModal: (show: boolean) => void;

  // Core operations
  generateFromPrompt: (prompt: string) => Promise<void>;
  editContent: (prompt: string) => Promise<void>;
  linkAllPages: () => Promise<void>;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const extractHtmlFromResponse = (response: string): string => {
  if (!response) return '';

  // Look for HTML content between ```html and ``` markers
  const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch) {
    return htmlMatch[1].trim();
  }

  // Look for HTML starting with DOCTYPE or html tag
  const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
  if (doctypeMatch) {
    return doctypeMatch[1].trim();
  }

  const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
  if (htmlTagMatch) {
    return htmlTagMatch[1].trim();
  }

  // If response contains HTML tags, assume it's HTML
  if (response.includes('<') && response.includes('>')) {
    const firstTagIndex = response.indexOf('<');
    return response.substring(firstTagIndex).trim();
  }

  return response;
};

const addInteractionDetection = (html: string): string => {
  // Remove any existing scripts first
  let cleanHtml = html
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<!-- INTERACTION_DETECTION_ADDED -->/g, '');

  const interactionScript = `
    <script>
      console.log('🔥 Interaction detection script loaded');

      // Add interaction detection after DOM is loaded
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🔥 DOM loaded, setting up interaction detection');

        // Function to check if element is navigation
        function isNavigationElement(element) {
          const navSelectors = ['nav', 'header', '.nav', '.navbar', '.navigation', '.menu'];
          const tagName = element.tagName.toLowerCase();
          const className = element.className || '';
          const id = element.id || '';

          // Check if element or parent is navigation
          if (tagName === 'nav' || tagName === 'header') return true;
          if (className.includes('nav') || className.includes('menu')) return true;
          if (id.includes('nav') || id.includes('menu')) return true;

          // Check parent elements
          let parent = element.parentElement;
          while (parent && parent !== document.body) {
            const parentTag = parent.tagName.toLowerCase();
            const parentClass = parent.className || '';
            const parentId = parent.id || '';

            if (parentTag === 'nav' || parentTag === 'header') return true;
            if (parentClass.includes('nav') || parentClass.includes('menu')) return true;
            if (parentId.includes('nav') || parentId.includes('menu')) return true;

            parent = parent.parentElement;
          }

          return false;
        }

        // Function to check if element needs implementation
        function needsImplementation(element) {
          const tagName = element.tagName.toLowerCase();
          const className = element.className || '';
          const textContent = element.textContent?.trim() || '';

          // Check if element has any event listeners (more comprehensive check)
          const onclickAttr = element.getAttribute('onclick');
          const hasEventListeners = element.onclick ||
                                   onclickAttr ||
                                   element.getAttribute('data-bs-toggle') || // Bootstrap modals
                                   element.getAttribute('data-toggle') ||    // Bootstrap modals
                                   element.getAttribute('data-target') ||    // Bootstrap targets
                                   element.getAttribute('data-bs-target') || // Bootstrap 5 targets
                                   (element._listeners && Object.keys(element._listeners).length > 0); // jQuery/custom listeners

          // Check if onclick calls openModal function (Readdy.ai pattern)
          const callsOpenModal = onclickAttr && onclickAttr.includes('openModal(');

          // If it calls openModal, check if the modal exists and the function is defined
          if (callsOpenModal) {
            const modalIdMatch = onclickAttr.match(/openModal\(['"]([^'"]+)['"]\)/);
            if (modalIdMatch) {
              const modalId = modalIdMatch[1];
              const modalExists = document.getElementById(modalId);
              const functionExists = typeof window.openModal === 'function';

              if (modalExists && functionExists) {
                return { needs: false }; // Modal is properly implemented
              }
            }
          }

          // Interactive elements that might need implementation
          if (tagName === 'button' && !hasEventListeners) {
            // Check if button opens a modal by looking for nearby modal elements
            const buttonId = element.id;
            const buttonText = textContent.toLowerCase();

            // Check if there's a modal with matching ID or content
            const relatedModal = document.querySelector(\`#\${buttonId}Modal, #\${buttonId}-modal, [id*="\${buttonText}"], .modal\`);
            if (relatedModal) {
              return { needs: false }; // Modal exists, button is implemented
            }

            return { needs: true, type: 'button', reason: 'Button without click handler' };
          }

          if (tagName === 'a') {
            const href = element.href || element.getAttribute('href');
            if (!href || href === '#' || href === 'javascript:void(0)') {
              // Check if it's a modal trigger or has other functionality
              if (hasEventListeners) {
                return { needs: false };
              }
              return { needs: true, type: 'link', reason: 'Link without proper href' };
            }
          }

          if (tagName === 'form' && !element.onsubmit && !element.getAttribute('onsubmit') && !element.action) {
            return { needs: true, type: 'form', reason: 'Form without action or handler' };
          }

          // Check for elements with click-like classes but no handlers
          if (className.includes('btn') || className.includes('button') || className.includes('clickable')) {
            if (!hasEventListeners) {
              return { needs: true, type: 'interactive', reason: 'Clickable element without handler' };
            }
          }

          return { needs: false };
        }

        // Add click listeners only to potentially interactive elements
        document.addEventListener('click', function(event) {
          const element = event.target;
          console.log('🔥 Element clicked:', element);

          // Check if this element actually needs implementation
          const implementationCheck = needsImplementation(element);

          // Only proceed if element needs implementation or is navigation
          const isNav = isNavigationElement(element);

          if (!implementationCheck.needs && !isNav) {
            console.log('🔥 Element does not need implementation, ignoring');
            return;
          }

          // Get element information
          const elementInfo = {
            tagName: element.tagName,
            textContent: element.textContent?.trim() || '',
            className: element.className || '',
            id: element.id || '',
            href: element.href || '',
            isNavigation: isNav,
            isInteractive: implementationCheck.needs,
            implementationType: implementationCheck.type,
            implementationReason: implementationCheck.reason,
            selector: getElementSelector(element)
          };

          console.log('🔥 Element needs attention:', elementInfo);

          // Send message to parent window
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'ELEMENT_CLICKED',
              element: elementInfo
            }, '*');
          }

          // Prevent default for navigation elements and unimplemented interactive elements
          if (elementInfo.isNavigation || implementationCheck.needs) {
            event.preventDefault();
          }
        });

        function getElementSelector(element) {
          if (element.id) return '#' + element.id;
          if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) return '.' + classes[0];
          }
          return element.tagName.toLowerCase();
        }

        // Add visual indicators for unimplemented elements
        function addUnimplementedIndicators() {
          // Remove existing indicators first
          document.querySelectorAll('.unimplemented-indicator').forEach(el => {
            el.classList.remove('unimplemented-indicator');
            const tooltip = el.querySelector('.unimplemented-tooltip');
            if (tooltip) tooltip.remove();
          });

          // Add CSS for indicators (only once)
          if (!document.getElementById('unimplemented-styles')) {
            const style = document.createElement('style');
            style.id = 'unimplemented-styles';
            style.textContent = \`
              .unimplemented-indicator {
                position: relative;
                cursor: pointer !important;
              }
              .unimplemented-indicator::after {
                content: '⚡';
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ff6b35;
                color: white;
                border-radius: 50%;
                width: 16px;
                height: 16px;
                font-size: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                pointer-events: none;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
              }
              .unimplemented-indicator:hover::after {
                background: #ff4500;
                transform: scale(1.1);
                transition: all 0.2s ease;
              }
              .unimplemented-tooltip {
                position: absolute;
                background: #333;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                z-index: 1001;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
              }
              .unimplemented-indicator:hover .unimplemented-tooltip {
                opacity: 1;
              }
            \`;
            document.head.appendChild(style);
          }

          // Find and mark unimplemented elements
          const allElements = document.querySelectorAll('*');
          allElements.forEach(element => {
            const check = needsImplementation(element);
            if (check.needs) {
              element.classList.add('unimplemented-indicator');

              // Add tooltip
              const tooltip = document.createElement('div');
              tooltip.className = 'unimplemented-tooltip';
              tooltip.textContent = check.reason;
              tooltip.style.top = '-30px';
              tooltip.style.left = '50%';
              tooltip.style.transform = 'translateX(-50%)';
              element.style.position = 'relative';
              element.appendChild(tooltip);
            }
          });
        }

        // Function to refresh indicators (called after content changes)
        window.refreshUnimplementedIndicators = addUnimplementedIndicators;

        // Debug function to test modal functionality
        window.debugModalFunctionality = function() {
          console.log('🔍 Debugging modal functionality...');

          // Find all buttons
          const buttons = document.querySelectorAll('button');
          buttons.forEach((button, index) => {
            const onclickAttr = button.getAttribute('onclick');
            console.log(\`Button \${index + 1}:\`, {
              text: button.textContent?.trim(),
              hasOnclick: !!button.onclick,
              onclickAttr: onclickAttr,
              hasDataToggle: !!button.getAttribute('data-bs-toggle') || !!button.getAttribute('data-toggle'),
              hasDataTarget: !!button.getAttribute('data-bs-target') || !!button.getAttribute('data-target'),
              className: button.className
            });

            // Test if onclick attribute works
            if (onclickAttr) {
              console.log(\`Button \${index + 1} onclick attribute: "\${onclickAttr}"\`);

              // Check if the function exists
              const functionName = onclickAttr.match(/(\w+)\(/)?.[1];
              if (functionName && typeof window[functionName] === 'function') {
                console.log(\`✅ Function \${functionName} exists and is callable\`);
              } else {
                console.error(\`❌ Function \${functionName} not found or not callable\`);
              }
            }
          });

          // Find all modals
          const modals = document.querySelectorAll('.modal, [id*="modal"], [class*="modal"]');
          console.log(\`Found \${modals.length} modal elements:\`, modals);
          modals.forEach((modal, index) => {
            console.log(\`Modal \${index + 1}:\`, {
              id: modal.id,
              className: modal.className,
              display: window.getComputedStyle(modal).display,
              visibility: window.getComputedStyle(modal).visibility
            });
          });

          // Check for JavaScript functions
          const modalFunctions = ['openModal', 'closeModal', 'showModal', 'hideModal'];
          modalFunctions.forEach(funcName => {
            if (typeof window[funcName] === 'function') {
              console.log(\`✅ Function \${funcName} is available\`);
            } else {
              console.log(\`❌ Function \${funcName} not found\`);
            }
          });
        };

        // Test modal functionality
        window.testModalOpen = function(modalId) {
          console.log(\`🧪 Testing modal open for: \${modalId}\`);
          const modal = document.getElementById(modalId);
          if (modal) {
            modal.style.display = 'block';
            console.log(\`✅ Modal \${modalId} opened successfully\`);

            // Auto-close after 3 seconds for testing
            setTimeout(() => {
              modal.style.display = 'none';
              console.log(\`✅ Modal \${modalId} closed automatically\`);
            }, 3000);
          } else {
            console.error(\`❌ Modal \${modalId} not found\`);
          }
        };

        // Manual test function to force add indicators
        window.testAddIndicators = function() {
          console.log('🧪 Manually testing indicator addition...');
          addUnimplementedIndicators();
          const indicators = document.querySelectorAll('.unimplemented-indicator');
          console.log(\`✅ Added indicators to \${indicators.length} elements\`);

          // List all buttons for debugging
          const buttons = document.querySelectorAll('button');
          console.log(\`🔍 Found \${buttons.length} buttons total:\`);
          buttons.forEach((btn, i) => {
            console.log(\`  Button \${i + 1}: "\${btn.textContent?.trim()}" - onclick: \${btn.getAttribute('onclick') || 'none'}\`);
          });
        };

        // Add indicators after a short delay to ensure DOM is ready
        setTimeout(() => {
          console.log('🔥 Running addUnimplementedIndicators...');
          addUnimplementedIndicators();
          console.log('🔥 Indicators added, checking for elements...');
          const indicators = document.querySelectorAll('.unimplemented-indicator');
          console.log('🔥 Found', indicators.length, 'elements with indicators');
        }, 500);

        // Watch for DOM changes and refresh indicators
        const observer = new MutationObserver(function(mutations) {
          let shouldRefresh = false;
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
              shouldRefresh = true;
            }
          });

          if (shouldRefresh) {
            // Debounce the refresh to avoid too many calls
            clearTimeout(window.indicatorRefreshTimeout);
            window.indicatorRefreshTimeout = setTimeout(addUnimplementedIndicators, 1000);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ['onclick', 'data-bs-toggle', 'data-toggle', 'data-target', 'data-bs-target']
        });
      });
    </script>
    <!-- INTERACTION_DETECTION_ADDED -->
  `;

  // Insert script before closing body tag, or at the end if no body tag
  if (cleanHtml.includes('</body>')) {
    cleanHtml = cleanHtml.replace('</body>', `${interactionScript}\n</body>`);
  } else {
    cleanHtml += interactionScript;
  }

  console.log('🔥 Interaction script injected, content length:', cleanHtml.length);
  console.log('🔥 Script includes indicators?', cleanHtml.includes('addUnimplementedIndicators'));

  return cleanHtml;
};

const ensureCompleteHtml = (content: string): string => {
  if (!content.includes('<!DOCTYPE html')) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${content}
</body>
</html>`;
  }
  return content;
};

// ============================================================================
// MAIN HOOK
// ============================================================================

export function useEditorV3() {
  // ============================================================================
  // STATE
  // ============================================================================

  const [state, setState] = useState<EditorState>({
    // Content state
    htmlContent: '',
    streamingContent: '',
    stableIframeContent: '',

    // UI state
    viewMode: 'preview',
    isGenerating: false,
    isLinking: false,

    // Multi-page state
    pages: [
      { id: 'main', name: 'Main Page', content: '', isActive: true, lastUpdated: new Date() }
    ],
    currentPageId: 'main',

    // Chat state
    messages: [
      {
        role: 'assistant',
        content: 'Hi! I\'m your AI design assistant. Describe what you\'d like to create and I\'ll build it for you.',
        timestamp: new Date()
      }
    ],
    input: '',

    // Selection state
    selectedElement: null,
    showImplementModal: false
  });

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // ============================================================================
  // ACTIONS
  // ============================================================================

  const updateState = useCallback((updates: Partial<EditorState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const setHtmlContent = useCallback((content: string) => {
    updateState({ htmlContent: content });
  }, [updateState]);

  const setStreamingContent = useCallback((content: string) => {
    updateState({ streamingContent: content });
  }, [updateState]);

  const setStableIframeContent = useCallback((content: string) => {
    updateState({ stableIframeContent: content });
  }, [updateState]);

  const setViewMode = useCallback((mode: ViewMode) => {
    updateState({ viewMode: mode });
  }, [updateState]);

  const setIsGenerating = useCallback((generating: boolean) => {
    updateState({ isGenerating: generating });
  }, [updateState]);

  const setIsLinking = useCallback((linking: boolean) => {
    updateState({ isLinking: linking });
  }, [updateState]);

  const addPage = useCallback((page: Omit<Page, 'lastUpdated'>) => {
    const newPage = { ...page, lastUpdated: new Date() };
    setState(prev => ({
      ...prev,
      pages: [...prev.pages, newPage]
    }));
  }, []);

  const updatePage = useCallback((pageId: string, updates: Partial<Page>) => {
    setState(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === pageId
          ? { ...page, ...updates, lastUpdated: new Date() }
          : page
      )
    }));
  }, []);

  const switchToPage = useCallback((pageId: string) => {
    setState(prev => {
      const page = prev.pages.find(p => p.id === pageId);
      if (page) {
        return {
          ...prev,
          currentPageId: pageId,
          htmlContent: extractHtmlFromResponse(page.content),
          stableIframeContent: page.content
        };
      }
      return prev;
    });
  }, []);

  const addMessage = useCallback((message: ChatMessage) => {
    setState(prev => ({
      ...prev,
      messages: [...prev.messages, message]
    }));
  }, []);

  const setInput = useCallback((input: string) => {
    updateState({ input });
  }, [updateState]);

  const clearInput = useCallback(() => {
    updateState({ input: '' });
  }, [updateState]);

  const setSelectedElement = useCallback((element: ElementInfo | null) => {
    updateState({ selectedElement: element });
  }, [updateState]);

  const setShowImplementModal = useCallback((show: boolean) => {
    updateState({ showImplementModal: show });
  }, [updateState]);

  // ============================================================================
  // CORE OPERATIONS
  // ============================================================================

  const generateFromPrompt = useCallback(async (prompt: string) => {
    console.log('🎯 generateFromPrompt called with prompt:', prompt.substring(0, 100) + '...');

    // Prevent duplicate calls
    if (state.isGenerating) {
      console.log('⚠️ Generation already in progress, skipping duplicate call');
      return;
    }

    setIsGenerating(true);
    setStreamingContent('');

    try {
      const response = await fetch('/api/llm/v3/generate-html', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ prompt })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

      addMessage({
        role: 'assistant',
        content: 'I\'ve created your design! What would you like to modify?',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Generation error:', error);
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setIsGenerating(false);
    }
  }, [state.isGenerating, setIsGenerating, setStreamingContent, setHtmlContent, addMessage]);

  const editContent = useCallback(async (prompt: string) => {
    const currentContent = state.htmlContent || state.stableIframeContent;
    if (!currentContent) {
      await generateFromPrompt(prompt);
      return;
    }

    setIsGenerating(true);
    setStreamingContent('');

    try {
      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          htmlContent: currentContent,
          prompt,
          conversationHistory: state.messages // Include conversation history like Readdy
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            } else if (event === 'context') {
              // Handle contextual understanding message
              isCollectingHTML = false; // Don't collect this as HTML
            }
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);
            if (isCollectingHTML) {
              accumulatedContent += data;
              setStreamingContent(accumulatedContent);
            } else {
              // This might be context data
              const contextData = data.trim();
              if (contextData) {
                // Add contextual understanding message to chat
                addMessage({
                  role: 'assistant',
                  content: contextData,
                  timestamp: new Date()
                });
              }
            }
          }
        }
      }

      addMessage({
        role: 'assistant',
        content: 'I\'ve updated your design. What would you like to change next?',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Edit error:', error);
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setIsGenerating(false);
    }
  }, [state.htmlContent, state.stableIframeContent, generateFromPrompt, setIsGenerating, setStreamingContent, setHtmlContent, addMessage]);

  const linkAllPages = useCallback(async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);
    if (pagesWithContent.length < 2) return;

    setIsLinking(true);

    try {
      for (const page of pagesWithContent) {
        const otherPageNames = pagesWithContent
          .filter(p => p.id !== page.id)
          .map(p => p.name);

        const prompt = `Update the navigation bar to include links to: ${otherPageNames.join(', ')}

🎯 REQUIREMENTS:
- Keep existing design exactly the same
- Add proper <a> tags for each page link
- Maintain responsive behavior
- Preserve all other content`;

        const response = await fetch('/api/llm/v3/edit', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            htmlContent: page.content,
            prompt
          })
        });

        if (response.ok) {
          // Process response and update page
          // Implementation similar to editContent but for specific page
        }

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error('Linking error:', error);
    } finally {
      setIsLinking(false);
    }
  }, [state.pages, setIsLinking]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Update stable iframe content when generation completes
  useEffect(() => {
    const cleanContent = extractHtmlFromResponse(state.htmlContent);
    if (cleanContent && !state.isGenerating && cleanContent.length > 50) {
      const completeHtml = ensureCompleteHtml(cleanContent);
      // Don't add interaction detection here - PreviewPanel will handle it via iframe injection

      console.log('🔥 Generation complete - updating content for page:', state.currentPageId);
      console.log('🔥 Content length:', completeHtml.length);

      // Update both iframe and page content atomically
      setState(prev => ({
        ...prev,
        stableIframeContent: completeHtml, // Use clean HTML, script will be injected by PreviewPanel
        pages: prev.pages.map(page =>
          page.id === prev.currentPageId
            ? { ...page, content: completeHtml, lastUpdated: new Date() }
            : page
        )
      }));
    }
  }, [state.htmlContent, state.isGenerating, state.currentPageId]);

  // Auto-scroll messages
  useEffect(() => {
    if (!state.isGenerating) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [state.messages, state.isGenerating]);

  // ============================================================================
  // RETURN
  // ============================================================================

  const actions: EditorActions = {
    setHtmlContent,
    setStreamingContent,
    setStableIframeContent,
    setViewMode,
    setIsGenerating,
    setIsLinking,
    addPage,
    updatePage,
    switchToPage,
    addMessage,
    setInput,
    clearInput,
    setSelectedElement,
    setShowImplementModal,
    generateFromPrompt,
    editContent,
    linkAllPages
  };

  return {
    state,
    actions,
    refs: {
      iframeRef,
      messagesEndRef
    },
    utils: {
      extractHtmlFromResponse,
      addInteractionDetection,
      ensureCompleteHtml
    }
  };
}
