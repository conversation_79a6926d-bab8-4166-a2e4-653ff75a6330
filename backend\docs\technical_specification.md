# JustPrototype Technical Specification: Readdy-Style Intent Generation

## Executive Summary

This specification outlines the implementation of a Readdy.ai-inspired intent generation system for JustPrototype, achieving 70-90% cost reduction in LLM usage while providing superior user experience through a two-phase approach: Element Extraction → Intent Generation → Implementation.

## System Architecture

### Core Components

```mermaid
graph TB
    A[Frontend UI] --> B[Element Selector]
    B --> C[Session Manager]
    C --> D[Element Extractor]
    D --> E[Intent Generator]
    E --> F[Implementation Engine]
    F --> G[Response Streamer]
    G --> A

    C --> H[(Session Database)]
    E --> I[LiteLLM Proxy]
    F --> I
```

### Data Flow

1. **Session Creation**: Store HTML once per prototype session
2. **Element Selection**: User clicks element, extract minimal context
3. **Intent Generation**: Analyze element to understand user intent
4. **Implementation**: Apply changes based on intent and user query
5. **Streaming Response**: Return modified HTML in real-time

## Database Schema

### Enhanced Session Management

```sql
-- Core session table (already implemented)
CREATE TABLE prototype_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prototype_id INTEGER NOT NULL REFERENCES prototypes(id),
    user_id INTEGER NOT NULL REFERENCES users(id),
    page_url TEXT NOT NULL,
    page_html TEXT NOT NULL,
    session_state VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);

-- Intent tracking for analytics and debugging
CREATE TABLE session_intents (
    id SERIAL PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES prototype_sessions(id) ON DELETE CASCADE,
    element_selector TEXT NOT NULL,
    element_code TEXT NOT NULL,
    generated_intent TEXT NOT NULL,
    user_query TEXT,
    implementation_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_time_ms INTEGER,
    token_usage JSONB
);

-- Element interaction tracking
CREATE TABLE element_interactions (
    id SERIAL PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES prototype_sessions(id) ON DELETE CASCADE,
    element_selector TEXT NOT NULL,
    interaction_type VARCHAR(50) NOT NULL, -- 'click', 'hover', 'select'
    element_context JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Service Layer Architecture

### 1. SessionService (✅ Implemented)
**Location**: `backend/services/sessionService.js`

**Responsibilities**:
- Session CRUD operations
- HTML storage and retrieval
- Session lifecycle management
- Automatic cleanup

**Key Methods**:
```javascript
class SessionService {
  async createSession({ prototype_id, user_id, page_url, page_html })
  async getSession(sessionId, userId)
  async getActiveSession(userId, prototypeId)
  async updateSessionState(sessionId, newState)
  async cleanupExpiredSessions()
}
```

### 2. ElementExtractor (🟡 Next Implementation)
**Location**: `backend/services/elementExtractor.js`

**Responsibilities**:
- Parse HTML and extract specific elements
- Generate CSS selectors for elements
- Validate and sanitize element code
- Provide element context information

**Key Methods**:
```javascript
class ElementExtractor {
  extractElement(html, selector)
  generateSelector(element)
  validateElement(elementCode)
  getElementContext(html, selector)
  sanitizeElementCode(elementCode)
}
```

### 3. IntentGenerator (🔴 Future Implementation)
**Location**: `backend/services/intentGenerator.js`

**Responsibilities**:
- Analyze clicked elements to understand user intent
- Generate structured intent descriptions
- Provide implementation suggestions
- Track intent generation metrics

**Key Methods**:
```javascript
class IntentGenerator {
  async generateIntent(elementCode, context)
  async validateIntent(intent)
  async refineIntent(intent, userFeedback)
  getIntentMetrics(sessionId)
}
```

### 4. ImplementationEngine (🔴 Future Implementation)
**Location**: `backend/services/implementationEngine.js`

**Responsibilities**:
- Apply changes based on generated intent
- Stream responses in real-time
- Handle rollback on errors
- Optimize HTML output

**Key Methods**:
```javascript
class ImplementationEngine {
  async implementIntent(sessionId, intent, userQuery)
  streamResponse(response, callback)
  rollbackChanges(sessionId, checkpointId)
  optimizeHTML(html)
}
```

## API Endpoints

### Phase 1: Intent Generation

**Based on Readdy Evidence** (`backend/reference/clikcandimplement.txt`):

```javascript
// Our implementation following Readdy's proven pattern
POST /api/intent/generate
Content-Type: application/json

{
  "sessionId": "uuid",
  "elementSelector": "button.support-btn",
  "elementCode": "<button class=\"bg-primary text-white px-4 py-2 rounded-button whitespace-nowrap flex items-center\"><div class=\"w-5 h-5 flex items-center justify-center mr-2\"><i class=\"ri-customer-service-2-line\"></i></div>Get Support</button>",
  "interactionType": "click"
}

// Response format matching Readdy's structure
Response:
{
  "success": true,
  "data": {
    "intentId": "intent-uuid",
    "userIntent": "The user clicked the 'Get Support' button in the welcome banner section. This suggests they need customer support or assistance with their banking services but the current page doesn't have a support dialog implementation.",
    "suggestion": "When the user clicks the 'Get Support' button, a support dialog modal should appear, similar to the existing connect-modal. This dialog should include support options like live chat with a customer service representative, phone support, email support, and a form to submit support tickets.",
    "confidence": 0.95,
    "estimatedTokens": 87,  // Based on 347 characters ÷ 4
    "canGenerate": true
  }
}
```

### Phase 2: Implementation

**Based on Readdy Evidence** (`backend/reference/clikcandimplement.txt` & `readdyresponse.txt`):

```javascript
// Our implementation following Readdy's session-based pattern
POST /api/prototype/edit
Content-Type: application/json

{
  "sessionId": "d47de6a6-a5fe-4661-8513-b349d01fa833",  // Session key like Readdy
  "intentId": "intent-uuid",
  "userQuery": "Add live chat support with file upload",
  "streamResponse": true,
  "style": "light",
  "framework": "html"
}

// Response: Server-Sent Events (matching Readdy's format)
event: startMsg
data: I

event: startMsg
data: '

event: startMsg
data: l

event: startMsg
data: l

// ... character by character streaming continues ...

event: startMsgFinish
data:

event: data
data: <!DOCTYPE html>

event: data
data: <html lang="en">

event: data
data: <head>

// ... complete HTML streamed line by line (4,486 lines like Readdy) ...

event: complete
data: {"success": true, "tokensUsed": 45000, "htmlLines": 4486}
```

## Frontend Integration

### Element Selection System

```javascript
// Element selection handler
class ElementSelector {
  constructor(sessionId) {
    this.sessionId = sessionId;
    this.setupEventListeners();
  }

  setupEventListeners() {
    document.addEventListener('click', this.handleElementClick.bind(this));
  }

  async handleElementClick(event) {
    if (!event.ctrlKey) return; // Require Ctrl+Click for selection

    event.preventDefault();
    event.stopPropagation();

    const element = event.target;
    const selector = this.generateSelector(element);
    const elementCode = element.outerHTML;

    // Generate intent
    const intent = await this.generateIntent(selector, elementCode);

    // Show intent to user for confirmation
    this.showIntentDialog(intent);
  }

  generateSelector(element) {
    // Generate unique CSS selector for element
    // Implementation details...
  }

  async generateIntent(selector, elementCode) {
    const response = await fetch('/api/intent/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sessionId: this.sessionId,
        elementSelector: selector,
        elementCode: elementCode,
        interactionType: 'click'
      })
    });
    return response.json();
  }
}
```

### Streaming Response Handler

```javascript
class StreamingHandler {
  constructor(sessionId) {
    this.sessionId = sessionId;
  }

  async implementIntent(intentId, userQuery) {
    const eventSource = new EventSource(`/api/prototype/edit?sessionId=${this.sessionId}&intentId=${intentId}&query=${encodeURIComponent(userQuery)}`);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleStreamData(data);
    };

    eventSource.addEventListener('complete', (event) => {
      const result = JSON.parse(event.data);
      this.handleComplete(result);
      eventSource.close();
    });
  }

  handleStreamData(data) {
    // Update UI with streaming HTML
    if (data.html) {
      this.updatePreview(data.html);
    }
  }
}
```

## Performance Optimization

### Token Usage Optimization

```javascript
// Token usage tracking and optimization
class TokenOptimizer {
  static optimizeElementCode(elementCode) {
    // Remove unnecessary attributes
    // Minify inline styles
    // Compress whitespace
    return optimizedCode;
  }

  static estimateTokens(text) {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  static shouldUseCache(elementCode) {
    // Cache common elements to avoid re-processing
    return this.isCommonElement(elementCode);
  }
}
```

### Caching Strategy

```javascript
// Intent caching for common elements
class IntentCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 1000;
  }

  getCachedIntent(elementHash) {
    return this.cache.get(elementHash);
  }

  setCachedIntent(elementHash, intent) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(elementHash, intent);
  }

  generateElementHash(elementCode) {
    // Generate hash based on element structure, not content
    return crypto.createHash('md5').update(elementCode).digest('hex');
  }
}
```

## Error Handling and Rollback

### Session State Management

```javascript
class SessionStateManager {
  async createCheckpoint(sessionId) {
    const session = await sessionService.getSession(sessionId);
    const checkpointId = uuidv4();

    await this.saveCheckpoint(checkpointId, session.page_html);
    return checkpointId;
  }

  async rollbackToCheckpoint(sessionId, checkpointId) {
    const checkpointData = await this.getCheckpoint(checkpointId);
    await sessionService.updateSessionHTML(sessionId, checkpointData.html);
  }

  async cleanupOldCheckpoints(sessionId) {
    // Remove checkpoints older than 1 hour
    const cutoff = new Date(Date.now() - 60 * 60 * 1000);
    await this.deleteCheckpointsOlderThan(sessionId, cutoff);
  }
}
```

## Security Considerations

### Input Validation

```javascript
class SecurityValidator {
  static validateElementCode(elementCode) {
    // Prevent XSS attacks
    const sanitized = DOMPurify.sanitize(elementCode);

    // Check for malicious patterns
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(sanitized)) {
        throw new Error('Potentially malicious element code detected');
      }
    }

    return sanitized;
  }

  static validateUserQuery(query) {
    // Limit query length
    if (query.length > 1000) {
      throw new Error('Query too long');
    }

    // Check for injection attempts
    const sqlPatterns = [/union\s+select/i, /drop\s+table/i];
    for (const pattern of sqlPatterns) {
      if (pattern.test(query)) {
        throw new Error('Invalid query detected');
      }
    }

    return query.trim();
  }
}
```

## Monitoring and Analytics

### Performance Metrics

```javascript
class PerformanceMonitor {
  static trackIntentGeneration(sessionId, metrics) {
    // Track token usage, response time, success rate
    console.log(`Intent generation for ${sessionId}:`, {
      tokensUsed: metrics.tokensUsed,
      responseTime: metrics.responseTime,
      success: metrics.success
    });
  }

  static trackImplementation(sessionId, metrics) {
    // Track implementation success, HTML size changes
    console.log(`Implementation for ${sessionId}:`, {
      htmlSizeBefore: metrics.htmlSizeBefore,
      htmlSizeAfter: metrics.htmlSizeAfter,
      implementationTime: metrics.implementationTime
    });
  }
}
```

## Next Steps

1. **Complete Element Extractor Service** (Task 2.1)
2. **Implement Intent Generator Service** (Task 3.1)
3. **Build Implementation Engine** (Task 4.1)
4. **Add Frontend Integration** (Task 5.1)
5. **Performance Testing and Optimization** (Task 6.1)

## Evidence-Based Implementation Roadmap

### Proven Cost Savings (From Readdy Analysis):
- **97.6% Input Token Reduction**: 347 chars vs 50KB+ HTML
- **Session-Based Architecture**: No HTML resending after initial storage
- **Real-time Streaming**: 4,486-line complete implementations
- **Element-Level Precision**: Targeted modifications with full context

### Reference File Evidence:
1. **`clikcandimplement.txt`**: API call patterns and payload structures
2. **`readdyresponse.txt`**: Complete 4,486-line HTML output example
3. **`readyyedit.txt`**: Session-based editing workflow
4. **`readdyjs.txt`**: Frontend integration patterns

This specification provides the foundation for implementing a **cost-effective, high-performance intent generation system** that follows Readdy's **proven approach with documented evidence** while being tailored to JustPrototype's specific needs.

**Immediate Next Step**: Begin Task 2.1 (Element Extraction Service) to start building this evidence-based system.
