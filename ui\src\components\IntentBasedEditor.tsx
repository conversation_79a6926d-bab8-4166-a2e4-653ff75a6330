import React, { useState, useCallback, useRef, useEffect } from 'react';
import ElementSelector from './ElementSelector';
import IntentDisplay from './IntentDisplay';
import EditConfirmation from './EditConfirmation';
import { intentApiService, Intent, SessionData, StreamingCallbacks } from '../services/intentApiService';
import '../styles/elementSelection.css';
import '../styles/intentDisplay.css';

interface IntentBasedEditorProps {
  prototypeId: number;
  userId: number;
  initialHtml: string;
  pageUrl: string;
  onHtmlChange: (html: string) => void;
  onError?: (error: string) => void;
}

interface EditResult {
  success: boolean;
  html?: string;
  error?: string;
  tokensUsed?: number;
  processingTime?: number;
}

interface StreamingProgress {
  stage: string;
  progress: number;
  message?: string;
}

const IntentBasedEditor: React.FC<IntentBasedEditorProps> = ({
  prototypeId,
  userId,
  initialHtml,
  pageUrl,
  onHtmlChange,
  onError
}) => {
  // State management
  const [currentHtml, setCurrentHtml] = useState(initialHtml);
  const [session, setSession] = useState<SessionData | null>(null);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [currentIntent, setCurrentIntent] = useState<Intent | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);
  const [isProcessingEdit, setIsProcessingEdit] = useState(false);
  const [editResult, setEditResult] = useState<EditResult | null>(null);
  const [streamingProgress, setStreamingProgress] = useState<StreamingProgress | null>(null);
  
  // Refs
  const sessionInitialized = useRef(false);

  // Initialize session on component mount
  useEffect(() => {
    if (!sessionInitialized.current) {
      initializeSession();
      sessionInitialized.current = true;
    }
  }, []);

  // Update HTML when it changes externally
  useEffect(() => {
    setCurrentHtml(initialHtml);
  }, [initialHtml]);

  /**
   * Initialize session for Readdy-style editing
   */
  const initializeSession = async () => {
    try {
      const response = await intentApiService.createSession(
        prototypeId,
        userId,
        pageUrl,
        currentHtml
      );

      if (response.success && response.data) {
        setSession(response.data);
        console.log('Session initialized:', response.data.id);
      } else {
        onError?.('Failed to initialize editing session');
      }
    } catch (error) {
      console.error('Session initialization error:', error);
      onError?.('Failed to initialize editing session');
    }
  };

  /**
   * Handle element selection from ElementSelector
   */
  const handleElementSelection = useCallback(async (selector: string) => {
    if (!session) {
      onError?.('No active session. Please refresh the page.');
      return;
    }

    setSelectedElement(selector);
    setIsGeneratingIntent(true);
    setCurrentIntent(null);
    setEditResult(null);

    try {
      // Extract element code from current HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(currentHtml, 'text/html');
      const element = doc.querySelector(selector);
      
      if (!element) {
        onError?.('Selected element not found in HTML');
        setIsGeneratingIntent(false);
        return;
      }

      const elementCode = element.outerHTML;

      // Generate intent using Readdy approach
      const response = await intentApiService.generateIntent({
        sessionId: session.id,
        elementCode,
        elementSelector: selector,
        interactionType: 'click'
      });

      if (response.success && response.data) {
        setCurrentIntent(response.data);
      } else {
        onError?.(response.error?.message || 'Failed to generate intent');
      }
    } catch (error) {
      console.error('Intent generation error:', error);
      onError?.('Failed to analyze element intent');
    } finally {
      setIsGeneratingIntent(false);
    }
  }, [session, currentHtml, onError]);

  /**
   * Handle intent confirmation and implementation
   */
  const handleIntentConfirmation = useCallback(async (intent: Intent, userQuery?: string) => {
    if (!session) {
      onError?.('No active session. Please refresh the page.');
      return;
    }

    setIsProcessingEdit(true);
    setEditResult(null);
    setStreamingProgress({ stage: 'initializing', progress: 0 });

    const streamingCallbacks: StreamingCallbacks = {
      onStart: (message) => {
        setStreamingProgress({ stage: 'starting', progress: 5, message });
      },
      onProgress: (stage, progress, message) => {
        setStreamingProgress({ stage, progress, message });
      },
      onData: (chunk) => {
        // Real-time HTML updates could be implemented here
        // For now, we'll wait for completion
      },
      onComplete: (result) => {
        setEditResult({
          success: true,
          html: result.html,
          tokensUsed: result.tokensUsed,
          processingTime: result.processingTime
        });
        setStreamingProgress(null);
        setIsProcessingEdit(false);
      },
      onError: (error) => {
        setEditResult({
          success: false,
          error: error.message
        });
        setStreamingProgress(null);
        setIsProcessingEdit(false);
        onError?.(error.message);
      }
    };

    try {
      await intentApiService.implementIntent({
        sessionId: session.id,
        intentId: intent.id,
        userQuery: userQuery || intent.suggestion,
        streamResponse: true
      }, streamingCallbacks);
    } catch (error) {
      console.error('Intent implementation error:', error);
      setEditResult({
        success: false,
        error: error instanceof Error ? error.message : 'Implementation failed'
      });
      setStreamingProgress(null);
      setIsProcessingEdit(false);
    }
  }, [session, onError]);

  /**
   * Handle intent rejection
   */
  const handleIntentRejection = useCallback(() => {
    setCurrentIntent(null);
    setSelectedElement(null);
    setEditResult(null);
  }, []);

  /**
   * Handle intent refinement
   */
  const handleIntentRefinement = useCallback(async (refinedQuery: string) => {
    if (!currentIntent) return;

    // Re-trigger intent generation with refined query
    // For now, we'll just update the intent suggestion
    setCurrentIntent({
      ...currentIntent,
      suggestion: refinedQuery
    });
  }, [currentIntent]);

  /**
   * Handle edit acceptance
   */
  const handleEditAcceptance = useCallback(() => {
    if (editResult?.success && editResult.html) {
      setCurrentHtml(editResult.html);
      onHtmlChange(editResult.html);
      
      // Reset state
      setCurrentIntent(null);
      setSelectedElement(null);
      setEditResult(null);
    }
  }, [editResult, onHtmlChange]);

  /**
   * Handle edit rejection (undo)
   */
  const handleEditRejection = useCallback(() => {
    setEditResult(null);
    setCurrentIntent(null);
    setSelectedElement(null);
  }, []);

  /**
   * Handle edit retry
   */
  const handleEditRetry = useCallback(() => {
    if (currentIntent) {
      setEditResult(null);
      handleIntentConfirmation(currentIntent);
    }
  }, [currentIntent, handleIntentConfirmation]);

  return (
    <div className="intent-based-editor">
      {/* Session Status */}
      {!session && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Initializing Session
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Setting up intelligent editing session...</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      {session && !currentIntent && !isGeneratingIntent && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Ready for Intelligent Editing
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>Click on any element in the preview below to see what we can do with it.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Element Selector */}
      {session && (
        <ElementSelector
          html={currentHtml}
          onSelect={handleElementSelection}
        />
      )}

      {/* Intent Display */}
      <IntentDisplay
        intent={currentIntent}
        isLoading={isGeneratingIntent}
        onConfirm={handleIntentConfirmation}
        onReject={handleIntentRejection}
        onRefine={handleIntentRefinement}
      />

      {/* Edit Confirmation */}
      <EditConfirmation
        isProcessing={isProcessingEdit}
        result={editResult}
        onAccept={handleEditAcceptance}
        onReject={handleEditRejection}
        onRetry={handleEditRetry}
        streamingProgress={streamingProgress}
      />

      {/* Debug Info (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-4 bg-gray-100 rounded-md text-xs">
          <h4 className="font-semibold mb-2">Debug Info:</h4>
          <p>Session ID: {session?.id || 'Not initialized'}</p>
          <p>Selected Element: {selectedElement || 'None'}</p>
          <p>Intent Generated: {currentIntent ? 'Yes' : 'No'}</p>
          <p>Processing: {isProcessingEdit ? 'Yes' : 'No'}</p>
        </div>
      )}
    </div>
  );
};

export default IntentBasedEditor;
