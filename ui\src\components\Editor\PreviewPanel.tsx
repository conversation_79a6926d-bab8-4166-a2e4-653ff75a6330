/**
 * Production-ready Preview Panel Component
 * Handles code preview, iframe rendering, and view mode switching
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { FiCode, FiEye, FiRefreshCw, FiMaximize2, FiMinimize2 } from 'react-icons/fi';
import { ViewMode } from '../../hooks/useEditorV3';

// ============================================================================
// TYPES
// ============================================================================

interface PreviewPanelProps {
  htmlContent: string;
  streamingContent: string;
  stableIframeContent: string;
  viewMode: ViewMode;
  isGenerating: boolean;
  onViewModeChange: (mode: ViewMode) => void;
  onElementClick?: (element: any) => void;
  className?: string;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const formatHtmlCode = (html: string): string => {
  if (!html) return '';

  try {
    // If HTML is already well-formatted, preserve it
    if (html.includes('\n') && html.includes('  ')) {
      return html;
    }

    // Basic HTML formatting with proper indentation
    let formatted = html
      // Normalize whitespace but preserve content
      .replace(/>\s*</g, '>\n<')
      // Add newlines after opening tags (block elements)
      .replace(/(<(?!\/)(div|section|article|header|footer|nav|main|aside|h[1-6]|p|ul|ol|li|form|fieldset|table|thead|tbody|tr|td|th|html|head|body|script|style)[^>]*>)/gi, '$1\n')
      // Add newlines before closing tags
      .replace(/(<\/(div|section|article|header|footer|nav|main|aside|h[1-6]|p|ul|ol|li|form|fieldset|table|thead|tbody|tr|html|head|body|script|style)>)/gi, '\n$1')
      // Add newlines after self-closing tags
      .replace(/(<[^>]*\/>)/g, '$1\n')
      // Handle CSS and JS blocks specially
      .replace(/(<style[^>]*>)([\s\S]*?)(<\/style>)/gi, (_, openTag, content, closeTag) => {
        const formattedCSS = content
          .replace(/\s*{\s*/g, ' {\n  ')
          .replace(/;\s*/g, ';\n  ')
          .replace(/\s*}\s*/g, '\n}\n');
        return `${openTag}\n${formattedCSS}${closeTag}`;
      })
      .replace(/(<script[^>]*>)([\s\S]*?)(<\/script>)/gi, (_, openTag, content, closeTag) => {
        return `${openTag}\n${content}\n${closeTag}`;
      });

    // Split into lines and add proper indentation
    const lines = formatted.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    let indentLevel = 0;
    const indentedLines = lines.map(line => {
      // Handle CSS content
      if (line.includes('{') && !line.startsWith('<')) {
        return '  '.repeat(indentLevel + 1) + line;
      }
      if (line === '}' && !line.startsWith('<')) {
        return '  '.repeat(indentLevel + 1) + line;
      }
      if (line.includes(':') && line.includes(';') && !line.startsWith('<')) {
        return '  '.repeat(indentLevel + 2) + line;
      }

      // Decrease indent for closing tags
      if (line.startsWith('</')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }

      const indentedLine = '  '.repeat(indentLevel) + line;

      // Increase indent for opening tags (but not self-closing or closing tags)
      if (line.startsWith('<') && !line.startsWith('</') && !line.endsWith('/>') && !line.includes('<!DOCTYPE')) {
        // Don't increase indent for inline elements
        const inlineElements = ['a', 'span', 'strong', 'em', 'b', 'i', 'small', 'code', 'img', 'input', 'button'];
        const tagName = line.match(/<(\w+)/)?.[1]?.toLowerCase();
        if (!inlineElements.includes(tagName || '')) {
          indentLevel++;
        }
      }

      return indentedLine;
    });

    return indentedLines.join('\n');
  } catch (error) {
    console.error('Error formatting HTML:', error);
    return html;
  }
};

const extractHtmlFromResponse = (response: string): string => {
  if (!response) return '';

  // Look for HTML content between ```html and ``` markers
  const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch) {
    return htmlMatch[1].trim();
  }

  // Look for HTML starting with DOCTYPE or html tag
  const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
  if (doctypeMatch) {
    return doctypeMatch[1].trim();
  }

  const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
  if (htmlTagMatch) {
    return htmlTagMatch[1].trim();
  }

  // If response contains HTML tags, assume it's HTML
  if (response.includes('<') && response.includes('>')) {
    const firstTagIndex = response.indexOf('<');
    return response.substring(firstTagIndex).trim();
  }

  return response;
};

// ============================================================================
// SUB-COMPONENTS
// ============================================================================

interface ViewModeToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

const ViewModeToggle: React.FC<ViewModeToggleProps> = ({ viewMode, onViewModeChange }) => (
  <div className="flex bg-gray-100 rounded-lg p-1">
    <button
      onClick={() => onViewModeChange('preview')}
      className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
        viewMode === 'preview'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <FiEye className="w-4 h-4 mr-1.5" />
      Preview
    </button>
    <button
      onClick={() => onViewModeChange('code')}
      className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
        viewMode === 'code'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <FiCode className="w-4 h-4 mr-1.5" />
      Code
    </button>
  </div>
);

interface PreviewIframeProps {
  content: string;
  isGenerating: boolean;
  onElementClick?: (element: any) => void;
}

const PreviewIframe: React.FC<PreviewIframeProps> = ({
  content,
  isGenerating,
  onElementClick
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scriptInjected, setScriptInjected] = useState(false);

  // Readdy.ai style interaction detection script
  const INTERACTION_DETECTION_SCRIPT = `
    (function() {
      console.log('🔥 Readdy-style interaction detection script loaded');

      function needsImplementation(element) {
        if (!element || !element.tagName) return { needs: false };

        const tagName = element.tagName.toLowerCase();
        const text = element.textContent?.trim() || '';

        // Check buttons without onclick handlers
        if (tagName === 'button' ||
            (tagName === 'input' && (element.type === 'button' || element.type === 'submit')) ||
            element.classList.contains('btn')) {

          const hasOnclick = element.onclick || element.getAttribute('onclick');
          console.log(\`🔍 Checking button "\${text}" - onclick: \${hasOnclick ? 'YES' : 'NO'}\`);

          if (!hasOnclick) {
            console.log(\`✅ Button "\${text}" needs implementation!\`);
            return {
              needs: true,
              type: 'button',
              reason: 'Button needs click functionality',
              text: text
            };
          }
        }

        // Check links without href or with placeholder href
        if (tagName === 'a') {
          const href = element.getAttribute('href');
          if (!href || href === '#' || href === 'javascript:void(0)') {
            console.log(\`✅ Link "\${text}" needs implementation!\`);
            return {
              needs: true,
              type: 'link',
              reason: 'Link needs destination',
              text: text
            };
          }
        }

        return { needs: false };
      }

      function addUnimplementedIndicators() {
        console.log('🔥 Adding unimplemented indicators...');

        // Add CSS for indicators
        if (!document.getElementById('unimplemented-styles')) {
          const style = document.createElement('style');
          style.id = 'unimplemented-styles';
          style.textContent = \`
            .unimplemented-indicator {
              position: relative !important;
              cursor: pointer !important;
            }
            .unimplemented-indicator::after {
              content: '⚡';
              position: absolute;
              top: -8px;
              right: -8px;
              background: #ff6b35;
              color: white;
              border-radius: 50%;
              width: 16px;
              height: 16px;
              font-size: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 1000;
              pointer-events: none;
              box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .unimplemented-indicator:hover::after {
              background: #ff4500;
              transform: scale(1.1);
              transition: all 0.2s ease;
            }
          \`;
          document.head.appendChild(style);
        }

        // Find and mark unimplemented elements
        const allElements = document.querySelectorAll('*');
        let indicatorCount = 0;

        allElements.forEach(element => {
          const check = needsImplementation(element);
          if (check.needs) {
            element.classList.add('unimplemented-indicator');
            indicatorCount++;

            // Add click handler for implementation modal
            element.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();

              console.log('🔥 Unimplemented element clicked:', check);

              // Send message to parent window
              window.parent.postMessage({
                type: 'ELEMENT_CLICKED',
                element: {
                  textContent: check.text,
                  implementationType: check.type,
                  implementationReason: check.reason
                }
              }, '*');
            });
          }
        });

        console.log(\`🔥 Added \${indicatorCount} unimplemented indicators\`);

        // Debug: List all buttons found
        const allButtons = document.querySelectorAll('button');
        console.log(\`🔍 Debug: Found \${allButtons.length} total buttons:\`);
        allButtons.forEach((btn, i) => {
          const hasOnclick = btn.onclick || btn.getAttribute('onclick');
          console.log(\`  Button \${i + 1}: "\${btn.textContent?.trim()}" - onclick: \${hasOnclick ? 'YES' : 'NO'}\`);
        });
      }

      // Make functions globally available
      window.addUnimplementedIndicators = addUnimplementedIndicators;
      window.refreshUnimplementedIndicators = addUnimplementedIndicators;

      // Initialize when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addUnimplementedIndicators);
      } else {
        addUnimplementedIndicators();
      }

      // Also run after a delay to catch dynamic content
      setTimeout(addUnimplementedIndicators, 500);
    })();
  `;

  // Inject script into iframe (Readdy.ai style)
  const injectInteractionScript = useCallback(() => {
    if (!iframeRef.current) return false;

    const iframe = iframeRef.current;
    if (!iframe?.contentDocument) return false;

    const doc = iframe.contentDocument;

    // Check if script is already injected
    if (doc.querySelector('#interaction-detection-script')) return true;

    try {
      // Create and inject script element
      const script = doc.createElement('script');
      script.id = 'interaction-detection-script';
      script.textContent = INTERACTION_DETECTION_SCRIPT;
      doc.body.appendChild(script);

      console.log('🔥 Interaction detection script injected successfully (Readdy style)');
      return true;
    } catch (error) {
      console.error('❌ Failed to inject interaction script:', error);
      return false;
    }
  }, [INTERACTION_DETECTION_SCRIPT]);

  // Handle iframe load event (Readdy.ai approach)
  useEffect(() => {
    if (!iframeRef.current) return;

    const handleIframeLoad = () => {
      console.log('🔥 Iframe loaded, injecting interaction script...');
      const success = injectInteractionScript();
      setScriptInjected(success);
    };

    const currentIframe = iframeRef.current;
    currentIframe.addEventListener('load', handleIframeLoad);

    // If iframe is already loaded, inject immediately
    if (currentIframe.contentDocument?.readyState === 'complete') {
      const success = injectInteractionScript();
      setScriptInjected(success);
    }

    return () => {
      currentIframe.removeEventListener('load', handleIframeLoad);
    };
  }, [injectInteractionScript]);

  // Handle iframe messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'ELEMENT_CLICKED') {
        onElementClick?.(event.data.element);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onElementClick]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'h-full'}`}>
      {/* Iframe Controls */}
      <div className="absolute top-2 right-2 z-10 flex space-x-2">
        <button
          onClick={() => iframeRef.current?.contentWindow?.location.reload()}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title="Refresh preview"
        >
          <FiRefreshCw className="w-4 h-4 text-gray-600" />
        </button>
        <button
          onClick={toggleFullscreen}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? (
            <FiMinimize2 className="w-4 h-4 text-gray-600" />
          ) : (
            <FiMaximize2 className="w-4 h-4 text-gray-600" />
          )}
        </button>
      </div>

      {/* Loading Overlay */}
      {isGenerating && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="flex items-center space-x-3 bg-white rounded-lg px-4 py-3 shadow-lg">
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
            <span className="text-sm font-medium text-gray-900">Generating...</span>
          </div>
        </div>
      )}

      {/* Iframe */}
      <iframe
        ref={iframeRef}
        srcDoc={content}
        className="w-full h-full border-0 bg-white"
        title="Preview"
        sandbox="allow-scripts allow-same-origin allow-modals allow-popups allow-forms"
      />

      {/* Debug info */}
      {scriptInjected && (
        <div className="absolute bottom-2 left-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
          ⚡ Interaction detection active
        </div>
      )}
    </div>
  );
};

interface CodeViewProps {
  content: string;
  isGenerating: boolean;
}

const CodeView: React.FC<CodeViewProps> = ({ content, isGenerating }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <div className="relative h-full bg-gray-900 rounded-lg overflow-hidden">
      {/* Code Header */}
      <div className="flex items-center justify-between bg-gray-800 px-4 py-2 border-b border-gray-700">
        <span className="text-sm font-medium text-gray-300">HTML</span>
        <button
          onClick={handleCopy}
          className="px-3 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors"
        >
          {copySuccess ? 'Copied!' : 'Copy'}
        </button>
      </div>

      {/* Code Content */}
      <div className="relative h-full overflow-auto">
        {isGenerating && (
          <div className="absolute inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-10">
            <div className="flex items-center space-x-3 bg-gray-800 rounded-lg px-4 py-3">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-400 border-t-transparent"></div>
              <span className="text-sm text-gray-300">Generating code...</span>
            </div>
          </div>
        )}

        <pre className="p-4 text-sm text-gray-300 font-mono leading-relaxed">
          <code>{content || '// No code generated yet'}</code>
        </pre>
      </div>
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const PreviewPanel: React.FC<PreviewPanelProps> = ({
  htmlContent,
  streamingContent,
  stableIframeContent,
  viewMode,
  isGenerating,
  onViewModeChange,
  onElementClick,
  className = ''
}) => {
  // Get current content for display
  const rawContent = streamingContent || htmlContent;
  const cleanHtmlContent = extractHtmlFromResponse(rawContent);
  const formattedCodeContent = formatHtmlCode(cleanHtmlContent);

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between bg-white border-b border-gray-200 px-6 py-4">
        <h2 className="text-lg font-semibold text-gray-900">Preview</h2>
        <ViewModeToggle viewMode={viewMode} onViewModeChange={onViewModeChange} />
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? (
          <PreviewIframe
            content={stableIframeContent}
            isGenerating={isGenerating}
            onElementClick={onElementClick}
          />
        ) : (
          <CodeView
            content={formattedCodeContent}
            isGenerating={isGenerating}
          />
        )}
      </div>
    </div>
  );
};

export default PreviewPanel;
