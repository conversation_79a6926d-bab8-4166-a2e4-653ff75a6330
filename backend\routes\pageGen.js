const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../auth/googleAuth');
const prototypeService = require('../services/prototypeService');
const sessionService = require('../services/sessionService');

/**
 * POST /api/page_gen/project/list
 * List projects with pagination (matching Readdy.ai API structure)
 * Request body: { page: { pageNum: number, pageSize: number } }
 * Response: { projects: [], totalCount: number, page: { pageNum, pageSize } }
 */
router.post('/project/list', ensureAuthenticated, async (req, res) => {
  try {
    const { page } = req.body;
    const pageNum = page?.pageNum || 1;
    const pageSize = page?.pageSize || 50;

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Calculate offset for pagination
    const offset = (pageNum - 1) * pageSize;

    // Get paginated prototypes (projects)
    const prototypes = await prototypeService.getPrototypesByUserPaginated(userId, pageSize, offset);
    const totalCount = await prototypeService.getPrototypesCountByUser(userId);

    // Transform prototypes to match project structure
    const projects = prototypes.map(prototype => ({
      id: prototype.id,
      title: prototype.title,
      description: prototype.description,
      created_at: prototype.created_at,
      updated_at: prototype.updated_at,
      preview_image_url: prototype.preview_image_url,
      // Add any additional fields that match the reference API
      status: 'active', // Default status
      type: 'prototype' // Project type
    }));

    res.json({
      projects,
      totalCount,
      page: {
        pageNum,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    });
  } catch (error) {
    console.error('Error listing projects:', error);
    res.status(500).json({ error: 'Failed to list projects' });
  }
});

/**
 * POST /api/page_gen/project/create
 * Create a new project (matching Readdy.ai API structure)
 * Request body: { title: string, description?: string, template?: string }
 * Response: { success: boolean, project: object }
 */
router.post('/project/create', ensureAuthenticated, async (req, res) => {
  try {
    const { title, description, template } = req.body;

    if (!title || typeof title !== 'string') {
      return res.status(400).json({ error: 'Project title is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Create basic HTML template based on template type
    let html = '<div>New Project</div>';
    if (template === 'landing') {
      html = `
        <div class="container">
          <header>
            <h1>${title}</h1>
            <nav>
              <a href="#home">Home</a>
              <a href="#about">About</a>
              <a href="#contact">Contact</a>
            </nav>
          </header>
          <main>
            <section>
              <h2>Welcome</h2>
              <p>${description || 'Welcome to our landing page'}</p>
            </section>
          </main>
        </div>
      `;
    } else if (template === 'dashboard') {
      html = `
        <div class="dashboard">
          <aside class="sidebar">
            <h2>Dashboard</h2>
            <nav>
              <a href="#overview">Overview</a>
              <a href="#analytics">Analytics</a>
              <a href="#settings">Settings</a>
            </nav>
          </aside>
          <main class="content">
            <h1>${title}</h1>
            <p>${description || 'Dashboard content goes here'}</p>
          </main>
        </div>
      `;
    }

    // Create the project using existing prototype service
    const prototype = await prototypeService.createPrototype({
      user_id: userId,
      title: title.substring(0, 250), // Ensure title fits DB constraint
      description: description || '',
      html: html,
      css: '', // Default empty CSS
      preview_image_url: null,
      prompt_id: null
    });

    // Transform to project format
    const project = {
      id: prototype.id,
      title: prototype.title,
      description: prototype.description,
      created_at: prototype.created_at,
      updated_at: prototype.updated_at,
      preview_image_url: prototype.preview_image_url,
      status: 'active',
      type: 'prototype'
    };

    res.json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

/**
 * POST /api/page_gen/session/list
 * List sessions (pages) for a project with pagination (matching Readdy.ai API structure)
 * Request body: { projectId: string, page: { pageNum: number, pageSize: number } }
 * Response: { sessions: [], totalCount: number, page: { pageNum, pageSize } }
 */
router.post('/session/list', ensureAuthenticated, async (req, res) => {
  try {
    const { projectId, page } = req.body;
    const pageNum = page?.pageNum || 1;
    const pageSize = page?.pageSize || 30;

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Verify that the project belongs to the user
    const project = await prototypeService.getPrototypeById(projectId, userId);
    if (!project) {
      return res.status(404).json({ error: 'Project not found or access denied' });
    }

    // Calculate offset for pagination
    const offset = (pageNum - 1) * pageSize;

    // Get paginated sessions for the project
    const sessions = await sessionService.getSessionsByProjectPaginated(projectId, userId, pageSize, offset);
    const totalCount = await sessionService.getSessionsCountByProject(projectId, userId);

    // Transform sessions to match the expected format
    const formattedSessions = sessions.map(session => ({
      id: session.id,
      projectId: session.prototype_id,
      pageUrl: session.page_url,
      sessionState: session.session_state,
      created_at: session.created_at,
      updated_at: session.updated_at,
      last_accessed: session.last_accessed,
      // Add additional fields that might be expected
      title: session.page_url.split('/').pop() || 'Untitled Page',
      type: 'page',
      status: session.session_state
    }));

    res.json({
      sessions: formattedSessions,
      totalCount,
      page: {
        pageNum,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    });
  } catch (error) {
    console.error('Error listing sessions:', error);
    res.status(500).json({ error: 'Failed to list sessions' });
  }
});

/**
 * POST /api/page_gen/session/get
 * Get a specific session/page by ID
 * Request body: { sessionId: string }
 * Response: { success: boolean, session: object }
 */
router.post('/session/get', ensureAuthenticated, async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Get session with user validation for security
    const sessionService = require('../services/sessionService');
    const session = await sessionService.getSession(sessionId, userId);

    if (!session) {
      return res.status(404).json({ error: 'Session not found or access denied' });
    }

    res.json({
      success: true,
      session: {
        id: session.id,
        prototype_id: session.prototype_id,
        page_url: session.page_url,
        page_html: session.page_html,
        session_state: session.session_state,
        created_at: session.created_at,
        updated_at: session.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting session:', error);
    res.status(500).json({ error: 'Failed to get session' });
  }
});

/**
 * POST /api/page_gen/project/update
 * Update an existing project
 * Request body: { projectId: string, title: string, description?: string }
 * Response: { success: boolean, project: object }
 */
router.post('/project/update', ensureAuthenticated, async (req, res) => {
  try {
    const { projectId, title, description } = req.body;

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    if (!title || typeof title !== 'string') {
      return res.status(400).json({ error: 'Project title is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Verify that the project belongs to the user
    const existingProject = await prototypeService.getPrototypeById(projectId, userId);
    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found or access denied' });
    }

    // Update the project
    const updatedPrototype = await prototypeService.updatePrototype(projectId, userId, {
      title: title.substring(0, 250), // Ensure title fits DB constraint
      description: description || existingProject.description
    });

    // Transform to project format
    const project = {
      id: updatedPrototype.id,
      title: updatedPrototype.title,
      description: updatedPrototype.description,
      created_at: updatedPrototype.created_at,
      updated_at: updatedPrototype.updated_at,
      preview_image_url: updatedPrototype.preview_image_url,
      status: 'active',
      type: 'prototype'
    };

    res.json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
});

/**
 * POST /api/page_gen/project/delete
 * Delete a project
 * Request body: { projectId: string }
 * Response: { success: boolean, message: string }
 */
router.post('/project/delete', ensureAuthenticated, async (req, res) => {
  try {
    const { projectId } = req.body;

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Verify that the project belongs to the user
    const existingProject = await prototypeService.getPrototypeById(projectId, userId);
    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found or access denied' });
    }

    // Delete the project
    await prototypeService.deletePrototype(projectId, userId);

    res.json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ error: 'Failed to delete project' });
  }
});

/**
 * POST /api/page_gen/session/rename
 * Rename a session/page
 * Request body: { sessionId: string, newTitle: string }
 * Response: { success: boolean, message: string }
 */
router.post('/session/rename', ensureAuthenticated, async (req, res) => {
  try {
    const { sessionId, newTitle } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }

    if (!newTitle || typeof newTitle !== 'string') {
      return res.status(400).json({ error: 'New title is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Get session with user validation for security
    const sessionService = require('../services/sessionService');
    const session = await sessionService.getSession(sessionId, userId);

    if (!session) {
      return res.status(404).json({ error: 'Session not found or access denied' });
    }

    // Update the session with new title (we'll store it in page_url for now)
    // In a real implementation, you might want to add a separate title field
    const { pool } = require('../services/promptDbService');
    await pool.query(
      'UPDATE prototype_sessions SET page_url = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND user_id = $3',
      [newTitle.trim(), sessionId, userId]
    );

    res.json({
      success: true,
      message: 'Session renamed successfully'
    });
  } catch (error) {
    console.error('Error renaming session:', error);
    res.status(500).json({ error: 'Failed to rename session' });
  }
});

/**
 * DELETE /api/page_gen/session/delete
 * Delete a session/page
 * Request body: { sessionId: string }
 * Response: { success: boolean, message: string }
 */
router.delete('/session/delete', ensureAuthenticated, async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.dbId || req.user.id;

    // Delete the session with user validation for security
    const sessionService = require('../services/sessionService');
    const success = await sessionService.deleteSession(sessionId, userId);

    if (!success) {
      return res.status(404).json({ error: 'Session not found or access denied' });
    }

    res.json({
      success: true,
      message: 'Session deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    res.status(500).json({ error: 'Failed to delete session' });
  }
});

/**
 * POST /api/page_gen/suggest/page_title
 * Generate a clean page title from user query using LLM (similar to Readdy.ai)
 * Request body: { query: string }
 * Response: { success: boolean, title: string }
 */
router.post('/suggest/page_title', ensureAuthenticated, async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Query is required' });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Use LLM service to generate a clean page title
    const llmServiceV3 = require('../services/llmServiceV3');

    const prompt = `Generate a clean, professional page title for the following user request.
The title should be:
- 2-4 words maximum
- Descriptive and clear
- Professional and concise
- Suitable for navigation menus
- No special characters or punctuation

User request: "${query}"

Examples:
- "create a login page" → "Login"
- "build a contact form" → "Contact"
- "make a pricing page with tiers" → "Pricing"
- "user dashboard with analytics" → "Dashboard"
- "about us page" → "About"
- "sign up form" → "Sign Up"

Return only the title, nothing else:`;

    // Use the LLM to generate a simple text response
    const llm = llmServiceV3.createLLM(null, false, 'title-generation');
    const { SystemMessage, HumanMessage } = require('@langchain/core/messages');

    const response = await llm.invoke([
      new SystemMessage('You are a helpful assistant that generates clean, professional page titles.'),
      new HumanMessage(prompt)
    ]);

    const result = {
      success: true,
      content: response.content
    };

    if (result.success && result.content) {
      // Clean the response to ensure it's just the title
      let title = result.content.trim();

      // Remove quotes if present
      title = title.replace(/^["']|["']$/g, '');

      // Remove any extra text after newlines
      title = title.split('\n')[0];

      // Capitalize properly
      title = title.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(' ');

      // Fallback if title is too long or empty
      if (!title || title.length > 50) {
        title = 'New Page';
      }

      res.json({
        success: true,
        title: title
      });
    } else {
      // Fallback to our existing logic
      const fallbackTitle = generatePageNameFromPrompt(query);
      res.json({
        success: true,
        title: fallbackTitle
      });
    }
  } catch (error) {
    console.error('Error generating page title:', error);

    // Fallback to our existing logic on error
    try {
      const fallbackTitle = generatePageNameFromPrompt(req.body.query || 'New Page');
      res.json({
        success: true,
        title: fallbackTitle
      });
    } catch (fallbackError) {
      res.status(500).json({ error: 'Failed to generate page title' });
    }
  }
});

// Helper function for fallback title generation
function generatePageNameFromPrompt(prompt) {
  const cleanPrompt = prompt.toLowerCase().trim();

  // Common patterns for page names
  if (cleanPrompt.includes('login') || cleanPrompt.includes('sign in') || cleanPrompt.includes('signin')) {
    return 'Login';
  }
  if (cleanPrompt.includes('signup') || cleanPrompt.includes('sign up') || cleanPrompt.includes('register')) {
    return 'Sign Up';
  }
  if (cleanPrompt.includes('contact') || cleanPrompt.includes('contact us')) {
    return 'Contact';
  }
  if (cleanPrompt.includes('about') || cleanPrompt.includes('about us')) {
    return 'About';
  }
  if (cleanPrompt.includes('pricing') || cleanPrompt.includes('price')) {
    return 'Pricing';
  }
  if (cleanPrompt.includes('service') || cleanPrompt.includes('services')) {
    return 'Services';
  }
  if (cleanPrompt.includes('product') || cleanPrompt.includes('products')) {
    return 'Products';
  }
  if (cleanPrompt.includes('dashboard') || cleanPrompt.includes('admin')) {
    return 'Dashboard';
  }
  if (cleanPrompt.includes('profile') || cleanPrompt.includes('account')) {
    return 'Profile';
  }
  if (cleanPrompt.includes('settings') || cleanPrompt.includes('setting')) {
    return 'Settings';
  }
  if (cleanPrompt.includes('help') || cleanPrompt.includes('support') || cleanPrompt.includes('faq')) {
    return 'Help';
  }
  if (cleanPrompt.includes('blog') || cleanPrompt.includes('news') || cleanPrompt.includes('article')) {
    return 'Blog';
  }
  if (cleanPrompt.includes('gallery') || cleanPrompt.includes('portfolio')) {
    return 'Gallery';
  }

  // Extract key words from prompt (first few meaningful words)
  const words = cleanPrompt
    .replace(/^(create|make|build|add|generate|design)\s+/i, '') // Remove action words
    .replace(/\s+(page|section|component|form|modal)\s*$/i, '') // Remove common suffixes
    .split(/\s+/)
    .filter(word => word.length > 2 && !['the', 'and', 'for', 'with', 'that', 'this'].includes(word))
    .slice(0, 2); // Take first 2 meaningful words

  if (words.length > 0) {
    return words.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }

  // Fallback
  return 'New Page';
}

module.exports = router;
