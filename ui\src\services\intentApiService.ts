/**
 * Intent-based API service following Readdy.ai approach
 * Handles session-based intent generation and implementation
 */

// ============================================================================
// TYPES
// ============================================================================

export interface Intent {
  id: string;
  userIntent: string;
  suggestion: string;
  confidence: number;
  canGenerate: boolean;
  estimatedTokens?: number;
  implementationType?: 'modal' | 'inline' | 'navigation';
  elementSelector?: string;
  sessionId?: string;
  pageUrl?: string;
}

export interface SessionData {
  id: string;
  prototypeId: number;
  userId: number;
  pageUrl: string;
  pageHtml: string;
  sessionState: 'active' | 'editing' | 'completed' | 'expired';
  createdAt: string;
  expiresAt: string;
}

export interface IntentGenerationRequest {
  sessionId: string;
  elementCode: string;
  elementSelector: string;
  interactionType?: 'click' | 'hover' | 'select';
}

export interface IntentImplementationRequest {
  sessionId: string;
  intentId: string;
  userQuery: string;
  streamResponse?: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    duration: number;
    timestamp: Date;
  };
}

export interface StreamingCallbacks {
  onStart?: (message: string) => void;
  onProgress?: (stage: string, progress: number, message?: string) => void;
  onData?: (chunk: string) => void;
  onComplete?: (result: any) => void;
  onError?: (error: Error) => void;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

const API_BASE_URL = '/api';
const DEFAULT_TIMEOUT = 30000;
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

class IntentApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'IntentApiError';
  }
}

const generateRequestId = (): string => 
  `intent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// ============================================================================
// INTENT API SERVICE CLASS
// ============================================================================

export class IntentApiService {
  private abortControllers: Map<string, AbortController> = new Map();

  // ============================================================================
  // SESSION MANAGEMENT
  // ============================================================================

  /**
   * Create a new prototype session
   */
  async createSession(
    prototypeId: number,
    userId: number,
    pageUrl: string,
    pageHtml: string
  ): Promise<ApiResponse<SessionData>> {
    const requestId = generateRequestId();
    
    try {
      const response = await this.makeRequest('/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prototype_id: prototypeId,
          user_id: userId,
          page_url: pageUrl,
          page_html: pageHtml
        })
      });

      const data = await response.json();
      
      return {
        success: true,
        data: data.session,
        metadata: {
          requestId,
          duration: 0,
          timestamp: new Date()
        }
      };
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  /**
   * Get session by ID
   */
  async getSession(sessionId: string): Promise<ApiResponse<SessionData>> {
    const requestId = generateRequestId();
    
    try {
      const response = await this.makeRequest(`/sessions/${sessionId}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      
      return {
        success: true,
        data: data.session,
        metadata: {
          requestId,
          duration: 0,
          timestamp: new Date()
        }
      };
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  // ============================================================================
  // INTENT GENERATION
  // ============================================================================

  /**
   * Generate intent from element selection (Phase 1 of Readdy approach)
   */
  async generateIntent(
    request: IntentGenerationRequest
  ): Promise<ApiResponse<Intent>> {
    const requestId = generateRequestId();
    
    try {
      const response = await this.makeRequest('/intent/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: request.sessionId,
          elementCode: request.elementCode,
          elementSelector: request.elementSelector,
          interactionType: request.interactionType || 'click'
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new IntentApiError(
          data.error || 'Intent generation failed',
          'INTENT_GENERATION_FAILED'
        );
      }

      return {
        success: true,
        data: {
          id: generateRequestId(),
          ...data.intent,
          elementSelector: request.elementSelector,
          sessionId: request.sessionId
        },
        metadata: {
          requestId,
          duration: 0,
          timestamp: new Date()
        }
      };
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  // ============================================================================
  // INTENT IMPLEMENTATION
  // ============================================================================

  /**
   * Implement intent with streaming response (Phase 2 of Readdy approach)
   */
  async implementIntent(
    request: IntentImplementationRequest,
    callbacks?: StreamingCallbacks
  ): Promise<ApiResponse<string>> {
    const requestId = generateRequestId();
    
    try {
      if (request.streamResponse && callbacks) {
        return await this.handleStreamingImplementation(request, callbacks, requestId);
      } else {
        return await this.handleStandardImplementation(request, requestId);
      }
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private async handleStreamingImplementation(
    request: IntentImplementationRequest,
    callbacks: StreamingCallbacks,
    requestId: string
  ): Promise<ApiResponse<string>> {
    const controller = new AbortController();
    this.abortControllers.set(requestId, controller);
    const startTime = Date.now();

    try {
      const response = await this.makeRequest('/prototype/edit-session', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        signal: controller.signal,
        body: JSON.stringify({
          sessionId: request.sessionId,
          intentId: request.intentId,
          userQuery: request.userQuery
        })
      });

      const reader = response.body?.getReader();
      if (!reader) {
        throw new IntentApiError('No response body available for streaming', 'NO_RESPONSE_BODY');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';
      let currentStage = 'initializing';
      let progress = 0;

      callbacks.onStart?.('Starting implementation...');

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            
            if (event === 'start') {
              currentStage = 'analyzing';
              progress = 10;
              callbacks.onProgress?.(currentStage, progress, 'Analyzing intent...');
            } else if (event === 'startMsg') {
              currentStage = 'generating';
              progress = 25;
              callbacks.onProgress?.(currentStage, progress, 'Generating response...');
            } else if (event === 'data') {
              currentStage = 'implementing';
              progress = Math.min(90, progress + 1);
              callbacks.onProgress?.(currentStage, progress, 'Implementing changes...');
            } else if (event === 'end') {
              progress = 100;
              callbacks.onProgress?.(currentStage, progress, 'Implementation complete');
              callbacks.onComplete?.({ 
                success: true, 
                html: accumulatedContent,
                tokensUsed: accumulatedContent.length / 4, // Rough estimate
                processingTime: Date.now() - startTime
              });
            }
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);
            accumulatedContent += data;
            callbacks.onData?.(data);
          }
        }
      }

      return {
        success: true,
        data: accumulatedContent,
        metadata: {
          requestId,
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      callbacks.onError?.(error instanceof Error ? error : new Error('Unknown streaming error'));
      throw error;
    } finally {
      this.abortControllers.delete(requestId);
    }
  }

  private async handleStandardImplementation(
    request: IntentImplementationRequest,
    requestId: string
  ): Promise<ApiResponse<string>> {
    const controller = new AbortController();
    this.abortControllers.set(requestId, controller);
    const startTime = Date.now();

    try {
      const response = await this.makeRequest('/prototype/edit-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        signal: controller.signal,
        body: JSON.stringify({
          sessionId: request.sessionId,
          intentId: request.intentId,
          userQuery: request.userQuery,
          streaming: false
        })
      });

      const data = await response.json();

      return {
        success: true,
        data: data.html || data.content || data,
        metadata: {
          requestId,
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      throw error;
    } finally {
      this.abortControllers.delete(requestId);
    }
  }

  private async makeRequest(
    endpoint: string,
    options: RequestInit
  ): Promise<Response> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const response = await fetch(url, {
          ...options,
          signal: options.signal
        });

        if (!response.ok) {
          throw new IntentApiError(
            `HTTP ${response.status}: ${response.statusText}`,
            'HTTP_ERROR',
            response.status
          );
        }

        return response;
      } catch (error) {
        if (attempt === MAX_RETRIES || error.name === 'AbortError') {
          throw error;
        }
        
        console.warn(`Request attempt ${attempt} failed, retrying...`, error);
        await delay(RETRY_DELAY * attempt);
      }
    }

    throw new IntentApiError('Max retries exceeded', 'MAX_RETRIES_EXCEEDED');
  }

  private handleError(error: any, requestId: string): ApiResponse<never> {
    const apiError = error instanceof IntentApiError ? error : new IntentApiError(
      error.message || 'Unknown error occurred',
      error.name || 'UNKNOWN_ERROR'
    );

    return {
      success: false,
      error: {
        code: apiError.code,
        message: apiError.message,
        details: apiError.details
      },
      metadata: {
        requestId,
        duration: 0,
        timestamp: new Date()
      }
    };
  }

  /**
   * Cancel an ongoing request
   */
  cancelRequest(requestId: string): void {
    const controller = this.abortControllers.get(requestId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(requestId);
    }
  }

  /**
   * Cancel all ongoing requests
   */
  cancelAllRequests(): void {
    for (const [requestId, controller] of this.abortControllers) {
      controller.abort();
    }
    this.abortControllers.clear();
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const intentApiService = new IntentApiService();
export default intentApiService;
