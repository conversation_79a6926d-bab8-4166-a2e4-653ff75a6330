import React, { useState, useRef, useEffect } from 'react';
import { FiChevronDown, FiPlus, FiFolder } from 'react-icons/fi';
import { Project } from '../services/pageGenService';

interface ProjectDropdownProps {
  projects: Project[];
  selectedProject: Project | null;
  onProjectSelect: (project: Project) => void;
  onCreateNew: () => void;
  isLoading?: boolean;
}

export function ProjectDropdown({ 
  projects, 
  selectedProject, 
  onProjectSelect, 
  onCreateNew,
  isLoading = false 
}: ProjectDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleProjectClick = (project: Project) => {
    onProjectSelect(project);
    setIsOpen(false);
  };

  const handleCreateNewClick = () => {
    onCreateNew();
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors disabled:opacity-50 min-w-[200px]"
      >
        <FiFolder className="w-4 h-4 text-gray-500" />
        <span className="flex-1 text-left text-sm text-gray-900 truncate">
          {isLoading ? 'Loading...' : selectedProject ? selectedProject.title : 'Select Project'}
        </span>
        <FiChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
          {/* Projects List */}
          {projects.length > 0 ? (
            <div className="py-1">
              {projects.map((project) => (
                <button
                  key={project.id}
                  onClick={() => handleProjectClick(project)}
                  className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors ${
                    selectedProject?.id === project.id ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <FiFolder className="w-4 h-4 text-gray-400" />
                    <span className="truncate">{project.title}</span>
                  </div>
                  {project.description && (
                    <div className="text-xs text-gray-500 mt-1 ml-6 truncate">
                      {project.description}
                    </div>
                  )}
                </button>
              ))}
              
              {/* Separator */}
              <div className="border-t border-gray-200 my-1"></div>
            </div>
          ) : (
            <div className="py-3 px-3 text-sm text-gray-500 text-center">
              No projects yet
            </div>
          )}

          {/* Create New Project */}
          <button
            onClick={handleCreateNewClick}
            className="w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 transition-colors flex items-center space-x-2"
          >
            <FiPlus className="w-4 h-4" />
            <span>Create New Project</span>
          </button>
        </div>
      )}
    </div>
  );
}
