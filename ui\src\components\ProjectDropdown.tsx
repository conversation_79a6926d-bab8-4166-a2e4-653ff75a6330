import React, { useState, useRef, useEffect } from 'react';
import { FiChevronDown, FiPlus, FiFolder, FiMoreVertical, FiEdit2, FiTrash2 } from 'react-icons/fi';
import { Project } from '../services/pageGenService';

interface ProjectDropdownProps {
  projects: Project[];
  selectedProject: Project | null;
  onProjectSelect: (project: Project) => void;
  onCreateNew: () => void;
  onProjectRename?: (project: Project, newName: string) => void;
  onProjectDelete?: (project: Project) => void;
  isLoading?: boolean;
}

export function ProjectDropdown({
  projects,
  selectedProject,
  onProjectSelect,
  onCreateNew,
  onProjectRename,
  onProjectDelete,
  isLoading = false
}: ProjectDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeProjectMenu, setActiveProjectMenu] = useState<number | null>(null);
  const [editingProject, setEditingProject] = useState<number | null>(null);
  const [editName, setEditName] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setActiveProjectMenu(null);
        setEditingProject(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleProjectClick = (project: Project) => {
    onProjectSelect(project);
    setIsOpen(false);
    setActiveProjectMenu(null);
  };

  const handleCreateNewClick = () => {
    onCreateNew();
    setIsOpen(false);
  };

  const handleProjectMenuClick = (e: React.MouseEvent, projectId: number) => {
    e.stopPropagation();
    setActiveProjectMenu(activeProjectMenu === projectId ? null : projectId);
  };

  const handleRenameClick = (project: Project) => {
    setEditingProject(project.id);
    setEditName(project.title);
    setActiveProjectMenu(null);
  };

  const handleRenameSubmit = (project: Project) => {
    if (editName.trim() && onProjectRename) {
      onProjectRename(project, editName.trim());
    }
    setEditingProject(null);
    setEditName('');
  };

  const handleRenameCancel = () => {
    setEditingProject(null);
    setEditName('');
  };

  const handleDeleteClick = (project: Project) => {
    if (onProjectDelete && confirm(`Are you sure you want to delete "${project.title}"?`)) {
      onProjectDelete(project);
    }
    setActiveProjectMenu(null);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors disabled:opacity-50 min-w-[250px]"
      >
        <FiFolder className="w-4 h-4 text-gray-500" />
        <span className="flex-1 text-left text-sm text-gray-900 truncate">
          {isLoading ? 'Loading...' : selectedProject ? selectedProject.title : 'Select Project'}
        </span>
        <FiChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
          {/* Projects List */}
          {projects.length > 0 ? (
            <div className="py-1">
              {projects.map((project) => (
                <div key={project.id} className="relative">
                  {editingProject === project.id ? (
                    /* Rename Input */
                    <div className="px-3 py-2">
                      <input
                        type="text"
                        value={editName}
                        onChange={(e) => setEditName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleRenameSubmit(project);
                          } else if (e.key === 'Escape') {
                            handleRenameCancel();
                          }
                        }}
                        onBlur={() => handleRenameSubmit(project)}
                        className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        autoFocus
                      />
                    </div>
                  ) : (
                    /* Project Item */
                    <div className="flex items-center group">
                      <button
                        onClick={() => handleProjectClick(project)}
                        className={`flex-1 text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors ${
                          selectedProject?.id === project.id ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <FiFolder className="w-4 h-4 text-gray-400" />
                          <span className="truncate">{project.title}</span>
                        </div>
                        {project.description && (
                          <div className="text-xs text-gray-500 mt-1 ml-6 truncate">
                            {project.description}
                          </div>
                        )}
                      </button>

                      {/* Project Menu Button */}
                      <div className="relative">
                        <button
                          onClick={(e) => handleProjectMenuClick(e, project.id)}
                          className="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <FiMoreVertical className="w-4 h-4" />
                        </button>

                        {/* Project Menu */}
                        {activeProjectMenu === project.id && (
                          <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                            <button
                              onClick={() => handleRenameClick(project)}
                              className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                            >
                              <FiEdit2 className="w-3 h-3" />
                              <span>Rename</span>
                            </button>
                            <button
                              onClick={() => handleDeleteClick(project)}
                              className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                            >
                              <FiTrash2 className="w-3 h-3" />
                              <span>Delete</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Separator */}
              <div className="border-t border-gray-200 my-1"></div>
            </div>
          ) : (
            <div className="py-3 px-3 text-sm text-gray-500 text-center">
              No projects yet
            </div>
          )}

          {/* Create New Project */}
          <button
            onClick={handleCreateNewClick}
            className="w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 transition-colors flex items-center space-x-2"
          >
            <FiPlus className="w-4 h-4" />
            <span>Create New Project</span>
          </button>
        </div>
      )}
    </div>
  );
}
